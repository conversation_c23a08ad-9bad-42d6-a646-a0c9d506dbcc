import pandas as pd
import torch
import fire
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm
import re
from typing import Union
import os


def format_prompt(row: pd.Series) -> list:
    """
    Formats a row from the ARC dataset into a list of messages
    for a chat model. This version is updated to prevent KeyError.
    """
    question = row["question"]

    # Build the list of choices dynamically, checking for None/NaN values safely.
    valid_choices = []
    if "choice_A" in row and pd.notna(row["choice_A"]):
        valid_choices.append(f"A) {row['choice_A']}")
    if "choice_B" in row and pd.notna(row["choice_B"]):
        valid_choices.append(f"B) {row['choice_B']}")
    if "choice_C" in row and pd.notna(row["choice_C"]):
        valid_choices.append(f"C) {row['choice_C']}")
    if "choice_D" in row and pd.notna(row["choice_D"]):
        valid_choices.append(f"D) {row['choice_D']}")

    content = (
        f"Please answer the following multiple-choice question by providing only the letter of the correct answer (A, B, C, or D).\n\n"
        f"Question: {question}\n\n"
        "Choices:\n" + "\n".join(valid_choices)
    )

    return [{"role": "user", "content": content}]


def extract_answer(generated_text: str) -> Union[str, None]:
    """
    Extracts the first single capital letter (A, B, C, or D) from the
    model's generated text.

    Args:
        generated_text (str): The decoded output from the model.

    Returns:
        Union[str, None]: The extracted letter (e.g., "A") or None if no valid letter is found.
    """
    # Search for the pattern: a capital letter A, B, C, or D, that might be
    # surrounded by parentheses, followed by a period or just on its own.
    match = re.search(r"\(?([A-D])\)?", generated_text)
    if match:
        return match.group(1)

    # Fallback for cases where the model just outputs the letter
    # This is a bit more lenient.
    first_char = generated_text.strip()
    if first_char and first_char[0] in ["A", "B", "C", "D"]:
        return first_char[0]

    return None


def evaluate_model_on_arc(
    model_id: str = "meta-llama/Meta-Llama-3.1-8B-Instruct",
    dataset_path: str = "arc_easy_processed.jsonl",
    results_dir: str = "evaluation_results",
    hf_token: str = None,
):
    """
    Evaluates a Hugging Face chat model on the processed ARC-Easy dataset
    and saves the correctly answered questions to a JSONL file.

    Args:
        model_id (str): The Hugging Face model ID to evaluate.
        dataset_path (str): The path to the JSONL dataset file.
        results_dir (str): Directory to save the per-model result files.
        hf_token (str): Optional Hugging Face Hub token for gated models.
    """
    print("--- Starting Model Evaluation on ARC-Easy ---")

    # 1. Setup device and model data type
    device = "cuda" if torch.cuda.is_available() else "cpu"
    dtype = (
        torch.bfloat16
        if torch.cuda.is_available() and torch.cuda.is_bf16_supported()
        else torch.float32
    )
    print(f"Using device: {device} with dtype: {dtype}")

    # 2. Load Tokenizer and Model
    print(f"Loading model: {model_id}...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_id, token=hf_token)
        model = AutoModelForCausalLM.from_pretrained(
            model_id, torch_dtype=dtype, device_map="auto", token=hf_token
        )
    except Exception as e:
        print(f"Error loading model: {e}")
        print(
            "Please ensure you are logged into Hugging Face CLI (`huggingface-cli login`) or provide a token if required."
        )
        return

    # 3. Load Dataset
    try:
        df = pd.read_json(dataset_path, lines=True)
        print(f"Loaded {len(df)} questions from '{dataset_path}'")
    except FileNotFoundError:
        print(f"Error: Dataset file not found at '{dataset_path}'")
        return

    # 4. Evaluation Loop
    correctly_answered_data = []

    stop_token_ids = [tokenizer.eos_token_id]
    if tokenizer.pad_token_id is None:
        tokenizer.pad_token_id = tokenizer.eos_token_id

    print("\n--- Running Evaluation ---")
    for _, row in tqdm(df.iterrows(), total=len(df)):
        # Format the prompt using the chat template
        messages = format_prompt(row)
        input_ids = tokenizer.apply_chat_template(
            messages, add_generation_prompt=True, return_tensors="pt"
        ).to(device)

        # Generate the output
        with torch.no_grad():
            outputs = model.generate(
                input_ids,
                max_new_tokens=10,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=stop_token_ids,
            )

        # Decode only the newly generated tokens
        response_ids = outputs[0][input_ids.shape[-1] :]
        response_text = tokenizer.decode(response_ids, skip_special_tokens=True)

        # Extract answer and check if correct
        model_answer = extract_answer(response_text)
        ground_truth = row["answerKey"]

        if model_answer and model_answer == ground_truth:
            # If correct, add the original row data to our list
            correctly_answered_data.append(row.to_dict())

    # 5. Save results to a file
    model_id_safe = model_id.replace("/", "_")
    os.makedirs(results_dir, exist_ok=True)
    results_filepath = os.path.join(
        results_dir, f"{model_id_safe}_correct_answers.jsonl"
    )

    if correctly_answered_data:
        results_df = pd.DataFrame(correctly_answered_data)
        results_df.to_json(results_filepath, orient="records", lines=True)
        print(f"\nSaved correctly answered questions to '{results_filepath}'")
    else:
        print(
            f"\nNo questions were answered correctly. An empty results file was not created."
        )

    # 6. Report Summary
    correct_predictions = len(correctly_answered_data)
    total_predictions = len(df)
    accuracy = (
        (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    )

    print("\n--- Evaluation Complete ---")
    print(f"Model ID:          {model_id}")
    print(f"Questions Evaluated: {total_predictions}")
    print(f"Correct Answers:     {correct_predictions}")
    print(f"Accuracy:            {accuracy:.2f}%")


if __name__ == "__main__":
    fire.Fire(evaluate_model_on_arc)
