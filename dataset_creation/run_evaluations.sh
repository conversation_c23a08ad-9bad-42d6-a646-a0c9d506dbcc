# =================================================================================
# Bash Script to Evaluate Multiple Models in Parallel Across Specific GPUs
# =================================================================================
#
# This script manages a job queue to run `evaluate_model.py` on a list of
# models, distributing the jobs across a specified set of GPUs. It ensures
# that no more jobs are running than there are available GPUs.
#
# It automatically loads the Hugging Face token from a `.env` file.
#

# --- Configuration ---

# Enable Job Control. This is crucial for the script to manage background processes.
set -m

# Load environment variables from a .env file if it exists.
if [ -f .env ]; then
  echo "Loading environment variables from .env file..."
  set -a # Automatically export all variables from sourced file
  source .env
  set +a # Stop automatically exporting
fi

# Define the specific GPU IDs to use.
AVAILABLE_GPUS=(3 4 5 6)

# An array of all Hugging Face model IDs to evaluate.
MODELS_TO_EVALUATE=(
  "meta-llama/Llama-3.1-8B-Instruct"
  "deepseek-ai/DeepSeek-R1-Distill-Llama-8B"
  "mistralai/Mistral-7B-Instruct-v0.3"
  "Qwen/Qwen3-8B"
  "google/gemma-2-2b-it"
  "google/gemma-3-4b-it"
  "google/gemma-2-9b-it"
  "google/gemma-3-12b-it"
)

# --- Script Logic ---

# Directory to store the log files
LOG_DIR="evaluation_logs"
mkdir -p "$LOG_DIR"

# Prepare the Hugging Face token argument
HF_TOKEN_ARG=""
if [ -n "$HF_TOKEN" ]; then
    echo "Hugging Face token found. Will use it for gated models."
    HF_TOKEN_ARG="--hf_token=$HF_TOKEN"
else
    echo "WARNING: HF_TOKEN not set. Gated models (Llama, Gemma) may fail."
fi

# Get the number of available GPUs to set the max number of parallel jobs
MAX_PARALLEL_JOBS=${#AVAILABLE_GPUS[@]}
echo "Starting batch evaluation..."
echo "Max parallel jobs: $MAX_PARALLEL_JOBS"
echo "Using GPUs: ${AVAILABLE_GPUS[*]}"
echo "--------------------------------------------------"

# --- Main Loop ---
# We will use a counter to assign GPUs in a round-robin fashion.
model_idx=0
for model_id in "${MODELS_TO_EVALUATE[@]}"; do
  # 1. Check if the number of running background jobs has reached the maximum.
  current_jobs=$(jobs -p | wc -l)
  echo "DEBUG: Checking job count. Currently $current_jobs running."

  while [[ $current_jobs -ge $MAX_PARALLEL_JOBS ]]; do
    echo "DEBUG: Max jobs ($MAX_PARALLEL_JOBS) reached. Waiting for a job to finish..."
    # `wait -n` pauses the script until *any* single background job finishes.
    # This is the key to our queueing system.
    wait -n
    # Recalculate job count after one has finished
    current_jobs=$(jobs -p | wc -l)
    echo "DEBUG: A job finished. New job count is $current_jobs."
  done

  # 2. Select the next GPU in a round-robin fashion
  gpu_id=${AVAILABLE_GPUS[$((model_idx % MAX_PARALLEL_JOBS))]}

  # 3. Prepare the log file path
  log_file_name=$(echo "$model_id" | tr '/' '_').log
  log_path="$LOG_DIR/$log_file_name"

  # 4. Launch the next evaluation in the background
  echo "=> Launching evaluation for: $model_id on GPU: $gpu_id"
  echo "   Log file: $log_path"

  # Set CUDA_VISIBLE_DEVICES for the command, then run it in the background with '&'
  (
    CUDA_VISIBLE_DEVICES=$gpu_id python evaluate_model.py --model_id="$model_id" $HF_TOKEN_ARG > "$log_path" 2>&1
  ) &

  # A small sleep can help prevent race conditions where the 'jobs' command
  # doesn't immediately register the new process.
  sleep 2

  # Increment the model index for the next round-robin assignment
  ((model_idx++))
done

# --- Final Wait and Summary ---
echo "--------------------------------------------------"
echo "All evaluation jobs have been launched."
echo "Waiting for the remaining jobs to complete..."

# `wait` with no arguments waits for ALL remaining background jobs to finish.
wait

echo "All evaluations are complete."
echo "--- Summary of Results ---"

# Scan log files for accuracy and errors to provide a quick summary
for log_file in "$LOG_DIR"/*.log; do
    echo "Results for: $(basename "$log_file" .log | tr '_' '/')"
    # Grep for the accuracy line, or show an error message.
    accuracy_line=$(grep "Accuracy:" "$log_file")
    if [[ -n "$accuracy_line" ]]; then
        echo "  $accuracy_line"
    else
        echo "  ERROR: Could not find accuracy. Check log for errors: $log_file"
    fi
done

echo "--------------------------------------------------"